import { SearchCondition } from '@/components/AdvancedSearch';
import { DatabaseFieldConfig } from '@/lib/configCache';

/**
 * Converts filter panel values to advanced search conditions
 * This enables automatic population of advanced search form with current filter state
 */
export function convertFiltersToConditions(
  filters: Record<string, unknown>,
  fieldConfigs: DatabaseFieldConfig[]
): SearchCondition[] {
  const conditions: SearchCondition[] = [];
  
  Object.entries(filters).forEach(([fieldName, value], index) => {
    // Skip empty values
    if (value === undefined || value === null || value === '' || 
        (Array.isArray(value) && value.length === 0)) {
      return;
    }
    
    // Find field configuration
    const fieldConfig = fieldConfigs.find(f => f.fieldName === fieldName);
    if (!fieldConfig || !fieldConfig.isFilterable) {
      return;
    }
    
    // Generate unique ID for condition
    const conditionId = `filter_${fieldName}_${Date.now()}_${index}`;
    
    // Determine operator based on field type and filter type
    let operator = 'contains';
    let conditionValue: string | string[] | { from?: string; to?: string } = '';
    
    switch (fieldConfig.filterType) {
      case 'select':
        operator = 'equals';
        conditionValue = String(value);
        break;

      case 'multi_select':
      case 'checkbox':
        if (Array.isArray(value) && value.length > 0) {
          operator = 'in';
          conditionValue = value.map(v => String(v));
        } else {
          return; // Skip empty arrays
        }
        break;

      case 'date_range':
        if (typeof value === 'object' && value !== null) {
          const dateRange = value as { from?: string; to?: string };
          if (dateRange.from || dateRange.to) {
            operator = 'between';
            conditionValue = dateRange;
          } else {
            return; // Skip empty date ranges
          }
        }
        break;

      case 'input':
      case 'range':
      default:
        operator = 'contains';
        conditionValue = String(value);
        break;
    }
    
    // Create condition
    const condition: SearchCondition = {
      id: conditionId,
      field: fieldName,
      operator,
      value: conditionValue,
      logic: conditions.length > 0 ? 'AND' : undefined
    };
    
    conditions.push(condition);
  });
  
  return conditions;
}

/**
 * Merges filter-derived conditions with existing advanced search conditions
 * Prevents duplication and maintains user's advanced search customizations
 */
export function mergeFilterAndAdvancedConditions(
  filterConditions: SearchCondition[],
  existingAdvancedConditions: SearchCondition[]
): SearchCondition[] {
  // Create a map of existing advanced conditions by field name
  const existingByField = new Map<string, SearchCondition>();
  existingAdvancedConditions.forEach(condition => {
    existingByField.set(condition.field, condition);
  });
  
  // Start with existing advanced conditions
  const mergedConditions = [...existingAdvancedConditions];
  
  // Add filter conditions that don't conflict with existing advanced conditions
  filterConditions.forEach(filterCondition => {
    if (!existingByField.has(filterCondition.field)) {
      // Update logic for proper chaining
      const updatedCondition: SearchCondition = {
        ...filterCondition,
        logic: mergedConditions.length > 0 ? 'AND' as const : undefined
      };
      mergedConditions.push(updatedCondition);
    }
  });
  
  return mergedConditions;
}

/**
 * Checks if a filter value represents an active filter
 */
export function isActiveFilter(value: unknown): boolean {
  if (value === undefined || value === null || value === '') {
    return false;
  }
  
  if (Array.isArray(value)) {
    return value.length > 0;
  }
  
  if (typeof value === 'object' && value !== null) {
    const obj = value as Record<string, unknown>;
    return Object.values(obj).some(v => v !== undefined && v !== null && v !== '');
  }
  
  return true;
}

/**
 * Gets display-friendly description of filter conditions
 */
export function getFilterConditionSummary(
  filters: Record<string, unknown>,
  fieldConfigs: DatabaseFieldConfig[]
): string {
  const activeFilters = Object.entries(filters)
    .filter(([_, value]) => isActiveFilter(value))
    .map(([fieldName, value]) => {
      const fieldConfig = fieldConfigs.find(f => f.fieldName === fieldName);
      const displayName = fieldConfig?.displayName || fieldName;
      
      if (Array.isArray(value)) {
        return `${displayName}: ${value.length} selected`;
      }
      
      if (typeof value === 'object' && value !== null) {
        const obj = value as { from?: string; to?: string };
        if (obj.from && obj.to) {
          return `${displayName}: ${obj.from} to ${obj.to}`;
        } else if (obj.from) {
          return `${displayName}: from ${obj.from}`;
        } else if (obj.to) {
          return `${displayName}: to ${obj.to}`;
        }
      }
      
      return `${displayName}: ${String(value)}`;
    });
    
  return activeFilters.join(', ');
}
